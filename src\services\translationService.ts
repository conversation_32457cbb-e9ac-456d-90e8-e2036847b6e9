import axios from 'axios';
import { LanguageConfig } from '../types';

// 最大并发请求数
const MAX_CONCURRENT_REQUESTS = 100;

interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

interface ChatResponse {
  choices: {
    message: {
      content: string;
    };
  }[];
}

// 请求队列管理器
class RequestQueueManager {
  private queue: Array<() => Promise<any>> = [];
  private activeRequests = 0;
  private maxConcurrent: number;

  constructor(maxConcurrent: number) {
    this.maxConcurrent = maxConcurrent;
  }

  // 添加请求到队列
  public enqueue<T>(requestFn: () => Promise<T>): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      // 将请求包装成一个任务
      const task = async () => {
        try {
          this.activeRequests++;
          const result = await requestFn();
          resolve(result);
          return result;
        } catch (error) {
          reject(error);
          throw error;
        } finally {
          this.activeRequests--;
          this.processQueue();
        }
      };

      // 将任务添加到队列
      this.queue.push(task);

      // 尝试处理队列
      this.processQueue();
    });
  }

  // 处理队列中的请求
  private processQueue() {
    // 如果当前活跃请求数小于最大并发数，且队列中有等待的请求
    while (this.activeRequests < this.maxConcurrent && this.queue.length > 0) {
      // 取出队列中的第一个请求并执行
      const nextTask = this.queue.shift();
      if (nextTask) {
        nextTask().catch(() => {
          // 错误已在任务内部处理
        });
      }
    }
  }

  // 获取当前队列状态
  public getStatus() {
    return {
      queueLength: this.queue.length,
      activeRequests: this.activeRequests
    };
  }
}

// 创建请求队列管理器实例
const requestQueue = new RequestQueueManager(MAX_CONCURRENT_REQUESTS);

// 导出获取队列状态的函数
export function getQueueStatus() {
  return requestQueue.getStatus();
}

// 默认语言提示
const languagePrompts: Record<string, string> = {
  'values': '将下面的中文翻译成英语，只返回翻译结果，不要加任何解释。（需保留所有的%s 和 斜杠n字符）：',
  'values-ja': '将下面的中文翻译成日语，只返回翻译结果，不要加任何解释或者读音注释（需保留所有的%s 和 斜杠n字符）：',
  'values-ko': '将下面的中文翻译成韩语，只返回翻译结果，不要加任何解释或者读音注释（需保留所有的%s 和 斜杠n字符）：',
  'values-pt': '将下面的中文翻译成葡萄牙语，只返回翻译结果，不要加任何解释（需保留所有的%s 和 斜杠n字符）：',
  'values-de': '将下面的中文翻译成德语，只返回翻译结果，不要加任何解释（需保留所有的%s 和 斜杠n字符）：',
  'values-tr': '将下面的中文翻译成土耳其语，只返回翻译结果，不要加任何解释（需保留所有的%s 和 斜杠n字符）：',
  'values-nl': '将下面的中文翻译成荷兰语，只返回翻译结果，不要加任何解释（需保留所有的%s 和 斜杠n字符）：',
  'values-es': '将下面的中文翻译成西班牙语，只返回翻译结果，不要加任何解释（需保留所有的%s 和 斜杠n字符）：',
  'values-ru': '将下面的中文翻译成俄语，只返回翻译结果，不要加任何解释（需保留所有的%s 和 斜杠n字符）：',
  'values-zh-rHK': '将下面的简体中文翻译成繁体中文，只返回翻译结果，不要加任何解释（需保留所有的%s 和 斜杠n字符）：',
  'values-th': '将下面的中文翻译成泰语，只返回翻译结果，不要加任何解释（需保留所有的%s 和 斜杠n字符）：',
  'values-vi': '将下面的中文翻译成越南语，只返回翻译结果，不要加任何解释（需保留所有的%s 和 斜杠n字符）：',
  'values-ar': '将下面的中文翻译成阿拉伯语，只返回翻译结果，不要加任何解释（需保留所有的%s 和 斜杠n字符）：',
  'values-fr': '将下面的中文翻译成法语，只返回翻译结果，不要加任何解释（需保留所有的%s 和 斜杠n字符）：',
  'values-it': '将下面的中文翻译成意大利语，只返回翻译结果，不要加任何解释（需保留所有的%s 和 斜杠n字符）：'
};

// 特殊语言处理
const specialLanguagePrompts: Record<string, string> = {
  'English': '将下面的中文翻译成英语，只返回翻译结果，不要加任何解释。（需保留所有的%s 和 斜杠n字符）：',
  '繁体中文': '将下面的简体中文翻译成繁体中文，只返回翻译结果，不要加任何解释。（需保留所有的%s 和 斜杠n字符）：',
  '中文': '原样保留文本，不要做任何修改：'
};

// 更新语言提示
export function updateLanguagePrompts(config: Record<string, LanguageConfig>): void {
  // 清除现有的提示
  Object.keys(languagePrompts).forEach(key => {
    delete languagePrompts[key];
  });

  // 添加新的提示
  Object.entries(config).forEach(([langKey, langConfig]) => {
    const label = langConfig.label;

    // 使用特殊语言提示或生成通用提示
    const prompt = specialLanguagePrompts[label] ||
                  `将下面的中文翻译成${label}，只返回翻译结果，不要加任何解释。（需保留所有的%s 和 斜杠n字符）：`;

    languagePrompts[langKey] = prompt;

    // 调试输出
    console.log(`为语言 ${label} (键: ${langKey}) 设置翻译提示: ${prompt}`);
  });

  console.log('已更新翻译提示，总数:', Object.keys(languagePrompts).length);
  console.log('所有语言键:', Object.keys(languagePrompts));
}

export async function translateText(text: string, targetLanguage: string): Promise<string> {
  // 使用请求队列管理器来控制并发
  return requestQueue.enqueue(async () => {
    try {
      // 获取队列状态
      const queueStatus = requestQueue.getStatus();
      console.log('翻译请求队列状态:', {
        当前活跃请求数: queueStatus.activeRequests,
        等待队列长度: queueStatus.queueLength,
        最大并发数: MAX_CONCURRENT_REQUESTS
      });

      console.log('准备翻译请求:', {
        原文: text,
        目标语言: targetLanguage,
        提示语: languagePrompts[targetLanguage]
      });

      const prompt = languagePrompts[targetLanguage] || '请将以下中文文本翻译成对应语言：';

      const result = await window.electronAPI.translateText({
        text,
        targetLanguage,
        prompt
      });

      if (!result) {
        throw new Error('翻译结果为空');
      }

      console.log('翻译成功:', {
        原文: text,
        译文: result,
        目标语言: targetLanguage
      });

      return result;

    } catch (error) {
      console.error('翻译服务错误:', {
        错误类型: error instanceof Error ? error.constructor.name : '未知错误类型',
        错误信息: error instanceof Error ? error.message : String(error),
        原始错误: error
      });

      // 处理特定的错误类型
      let errorMessage = '翻译失败';
      if (error instanceof Error) {
        if (error.message.includes('API 端点不存在')) {
          errorMessage = '翻译服务配置错误，请检查 API 地址';
        } else if (error.message.includes('API 认证失败')) {
          errorMessage = 'API 密钥无效，请检查配置';
        } else if (error.message.includes('调用频率超限')) {
          errorMessage = '翻译请求过于频繁，请稍后再试';
        } else {
          errorMessage = error.message;
        }
      }

      throw new Error(errorMessage);
    }
  });
}