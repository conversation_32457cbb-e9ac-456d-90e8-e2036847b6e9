import React, { useState, useEffect } from 'react';
import { Modal, Table, Button, Checkbox } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { LanguageData } from '../types';
import './TranslationSelector.scss';

interface TranslationSelectorProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (selectedRows: LanguageData[]) => void;
  data: LanguageData[];
}

export const TranslationSelector: React.FC<TranslationSelectorProps> = ({
  visible,
  onCancel,
  onOk,
  data
}) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [filteredData, setFilteredData] = useState<LanguageData[]>([]);

  useEffect(() => {
    // 筛选有中文但没有英文的行
    const filtered = data.filter(
      row => row.values['values-zh-rCN'] && !row.values['values']
    );
    setFilteredData(filtered);
    // 默认全选
    setSelectedRowKeys(filtered.map(row => row.key));
  }, [data, visible]);

  const columns: ColumnsType<LanguageData> = [
    {
      title: '模块',
      dataIndex: 'module',
      width: 150,
      ellipsis: true,
    },
    {
      title: 'Key',
      dataIndex: 'key',
      width: 200,
      ellipsis: true,
    },
    {
      title: '中文',
      dataIndex: ['values', 'values-zh-rCN'],
      width: 300,
      ellipsis: true,
    }
  ];

  const handleSelectAll = () => {
    setSelectedRowKeys(filteredData.map(row => row.key));
  };

  const handleUnselectAll = () => {
    setSelectedRowKeys([]);
  };

  const handleInvertSelection = () => {
    const allKeys = filteredData.map(row => row.key);
    const invertedKeys = allKeys.filter(key => !selectedRowKeys.includes(key));
    setSelectedRowKeys(invertedKeys);
  };

  const handleOk = () => {
    const selectedRows = filteredData.filter(row => 
      selectedRowKeys.includes(row.key)
    );
    onOk(selectedRows);
  };

  return (
    <Modal
      title="选择需要翻译的行"
      open={visible}
      onCancel={onCancel}
      width={800}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button 
          key="ok" 
          type="primary" 
          onClick={handleOk}
          disabled={selectedRowKeys.length === 0}
        >
          开始翻译
        </Button>
      ]}
    >
      <div className="translation-selector">
        <div className="selector-actions">
          <Button onClick={handleSelectAll}>全选</Button>
          <Button onClick={handleUnselectAll}>取消全选</Button>
          <Button onClick={handleInvertSelection}>反选</Button>
          <span className="selection-info">
            已选择 {selectedRowKeys.length} 行 / 共 {filteredData.length} 行
          </span>
        </div>
        <Table
          rowSelection={{
            selectedRowKeys,
            onChange: (selectedKeys) => 
              setSelectedRowKeys(selectedKeys as string[]),
          }}
          columns={columns}
          dataSource={filteredData}
          rowKey="key"
          size="small"
          scroll={{ y: 400 }}
          pagination={false}
        />
      </div>
    </Modal>
  );
}; 