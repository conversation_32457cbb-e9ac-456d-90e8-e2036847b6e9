# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependencies
node_modules/

# Build outputs
dist/
dist-ssr/
dist-electron/
release/
*.local

# Environment files
.env
.env.*
!.env.example

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/launch.json
.idea/
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Lock files (uncomment if you want to ignore them)
package-lock.json
pnpm-lock.yaml
yarn.lock

# Testing
/test-results/
/playwright-report/
/playwright/.cache/

# Coverage directory
coverage/

# Temporary files
.tmp/
.temp/
temp/
tmp/

# Debug files
*.debug

# Compiled output
*.compiled

# Cache
.cache/
.eslintcache
.stylelintcache

# Electron specific
out/

# OS specific files
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
