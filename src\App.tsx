import { useState, useEffect, useRef } from 'react'
import { TitleBar } from './components/TitleBar'
import { ImportExport } from './components/ImportExport'
import { LanguageTable } from './components/LanguageTable'
import { message } from 'antd'
import { AddRowModal } from './components/AddRowModal'
import './App.scss'
import { translateText, updateLanguagePrompts, getQueueStatus } from './services/translationService'
import { TranslationProgress } from './components/TranslationProgress'
import { TranslationSelector } from './components/TranslationSelector'
import { LanguageData, LanguageConfig } from './types'
import React from 'react'
import * as XLSX from 'xlsx';

// 默认语言配置（仅作为备用）
const DEFAULT_LANGUAGE_CONFIG: Record<string, LanguageConfig> = {
  'values-zh-rCN': { key: '中文_values-zh-rCN', label: '中文' },
  'values': { key: 'English_values', label: 'English' },
  'values-ja': { key: '日语_values-ja', label: '日语' },
  'values-ko': { key: '韩语_values-ko', label: '韩语' },
  'values-pt': { key: '葡萄牙_values-pt', label: '葡萄牙语' },
  'values-de': { key: '德语_values-de', label: '德语' },
  'values-tr': { key: '土耳其语_values-tr', label: '土耳其语' },
  'values-nl': { key: '荷兰语_values-nl', label: '荷兰语' },
  'values-es': { key: '西班牙语_values-es', label: '西班牙语' },
  'values-ru': { key: '俄罗斯语_values-ru', label: '俄罗斯语' },
  'values-zh-rHK': { key: '繁体中文_values-zh-rHK', label: '繁体中文' },
  'values-th': { key: '泰语_values-th', label: '泰语' },
  'values-vi': { key: '越南语_values-vi', label: '越南语' },
  'values-ar': { key: '阿拉伯语_values-ar', label: '阿拉伯语' },
  'values-fr': { key: '法语_values-fr', label: '法语' },
  'values-it': { key: '意大利语_values-it', label: '意大利语' },
  'values-fil': { key: '菲律宾语_values-fil', label: '菲律宾语' },
  'values-id': { key: '印尼语_values-id', label: '印尼语' }
}

// 从表头解析语言配置的函数
function parseLanguageConfigFromHeaders(headers: string[]): Record<string, LanguageConfig> {
  const config: Record<string, LanguageConfig> = {};

  console.log('开始解析表头:', headers);

  // 查找"模块"和"Key"的位置
  const moduleIndex = headers.findIndex(h => h === '模块' || h.toLowerCase() === 'module');
  const keyIndex = headers.findIndex(h => h === 'Key' || h.toLowerCase() === 'key');

  console.log('模块列索引:', moduleIndex, '键列索引:', keyIndex);

  // 如果找不到"模块"或"Key"列，尝试使用默认位置
  const startIndex = (moduleIndex >= 0 && keyIndex >= 0) ?
                     Math.max(moduleIndex, keyIndex) + 1 : 2;

  // 记录模块和Key的列名，用于后续数据处理
  const moduleColumnName = moduleIndex >= 0 ? headers[moduleIndex] : '模块';
  const keyColumnName = keyIndex >= 0 ? headers[keyIndex] : 'Key';
  console.log('模块列名:', moduleColumnName, '键列名:', keyColumnName);

  console.log('开始解析语言列的索引:', startIndex);

  // 从语言列开始解析
  for (let i = startIndex; i < headers.length; i++) {
    const header = headers[i];
    if (!header || header.trim() === '') continue;

    console.log(`处理表头 [${i}]: ${header}`);

    // 解析表头，支持任意格式的语言名称
    const parts = header.split('_');

    // 如果表头包含下划线，按照"语言名_values-xx"格式解析
    if (parts.length >= 2) {
      const label = parts[0]; // 语言名称（如"中文"、"菲律宾语"等）
      const langKeyParts = parts.slice(1); // 获取剩余部分作为语言键

      // 尝试多种可能的语言键格式
      let langKey = '';

      // 1. 检查是否包含"values"部分
      if (langKeyParts.some(part => part.includes('values'))) {
        // 使用完整的后缀作为语言键
        langKey = langKeyParts.join('_');
      }
      // 2. 如果没有"values"前缀，但有语言代码（如"fil"、"id"等）
      else if (langKeyParts.length > 0) {
        // 为语言代码添加"values-"前缀
        const langCode = langKeyParts[0];
        langKey = `values-${langCode}`;
      }

      // 如果成功提取了语言键
      if (langKey) {
        // 确保langKey是唯一的，避免覆盖
        let uniqueLangKey = langKey;
        let counter = 1;
        while (config[uniqueLangKey]) {
          uniqueLangKey = `${langKey}_${counter}`;
          counter++;
        }

        config[uniqueLangKey] = {
          key: header,
          label: label
        };

        console.log(`✅ 成功解析语言: ${label}, 键: ${uniqueLangKey}, 原始表头: ${header}`);
      } else {
        console.warn(`❌ 无法解析语言键: ${header}`);
      }
    }
    // 特殊处理没有下划线的表头（如"English"）
    else if (header.includes('English')) {
      config['values'] = {
        key: header,
        label: 'English'
      };
      console.log(`✅ 特殊处理英语: 键: values, 原始表头: ${header}`);
    }
    // 其他没有下划线的表头，尝试猜测语言
    else {
      // 尝试根据表头内容猜测语言
      let langKey = '';
      let label = header;

      if (header.includes('中文')) {
        langKey = 'values-zh-rCN';
      } else if (header.includes('繁体')) {
        langKey = 'values-zh-rHK';
      } else if (header.includes('日语') || header.includes('日本')) {
        langKey = 'values-ja';
      } else if (header.includes('韩语') || header.includes('韩国')) {
        langKey = 'values-ko';
      } else if (header.includes('英语') || header.includes('English')) {
        langKey = 'values';
      } else {
        // 对于无法识别的语言，使用一个通用格式
        langKey = `values-unknown-${i}`;
      }

      if (langKey) {
        config[langKey] = {
          key: header,
          label: label
        };
        console.log(`✅ 猜测语言: ${label}, 键: ${langKey}, 原始表头: ${header}`);
      }
    }
  }

  // 如果没有解析到任何语言配置，使用默认配置
  if (Object.keys(config).length === 0) {
    console.warn('⚠️ 未能从表头解析出语言配置，使用默认配置');
    return DEFAULT_LANGUAGE_CONFIG;
  }

  // 添加调试信息，显示所有解析出的语言
  console.log('✅ 解析出的所有语言配置:', config);
  console.log('✅ 解析出的语言数量:', Object.keys(config).length);
  console.log('✅ 表头中的语言数量:', headers.length - startIndex);

  return config;
}

interface OriginalExcelInfo {
  workbook: any
  sheetName: string
  headerRow: number
  fileName: string
}

const App: React.FC = () => {
  // 使用 useRef 保持数据引用
  const dataRef = useRef<LanguageData[]>([]);
  const [data, setData] = useState<LanguageData[]>([]);

  // 添加动态语言配置状态
  const [languageConfig, setLanguageConfig] = useState<Record<string, LanguageConfig>>(DEFAULT_LANGUAGE_CONFIG);

  // 同步数据到 ref
  useEffect(() => {
    dataRef.current = data;
    console.log('App 数据更新:', {
      时间: new Date().toISOString(),
      数据量: data.length
    });
  }, [data]);

  const [originalExcelInfo, setOriginalExcelInfo] = useState<OriginalExcelInfo | null>(null)
  const [addRowModalVisible, setAddRowModalVisible] = useState(false)
  const [translationProgress, setTranslationProgress] = useState({
    visible: false,
    current: 0,
    total: 0,
    currentLanguage: '',
    currentText: '',
    queueStatus: {
      activeRequests: 0,
      queueLength: 0
    }
  })
  const [selectorVisible, setSelectorVisible] = useState(false)

  // 添加数据状态监控
  useEffect(() => {
    console.log('数据状态变化:', {
      时间: new Date().toISOString(),
      数据总量: data.length,
      第一条数据: data[0]
    });
  }, [data]);

  // 使用 React.useMemo 缓存数据处理
  const processedData = React.useMemo(() => {
    return data.map(item => ({
      ...item,
      key: item.key // 确保每行都有唯一的 key
    }));
  }, [data]);

  const handleDataImported = async (jsonData: any[], originalInfo: any) => {
    try {
      if (!Array.isArray(jsonData) || jsonData.length === 0) {
        message.error('导入的数据无效');
        return;
      }

      // 从原始Excel工作表中获取表头
      const workbook = originalInfo.workbook;
      const sheetName = originalInfo.sheetName;
      const worksheet = workbook.Sheets[sheetName];
      const headerRow = originalInfo.headerRow || 0;

      console.log('Excel信息:', {
        工作表名: sheetName,
        表头行: headerRow,
        文件名: originalInfo.fileName
      });

      // 获取表头范围
      const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');
      console.log('Excel范围:', {
        开始行: range.s.r,
        开始列: range.s.c,
        结束行: range.e.r,
        结束列: range.e.c
      });

      const headers: string[] = [];

      // 读取表头
      for (let C = 0; C <= range.e.c; C++) {
        const cellRef = XLSX.utils.encode_cell({ r: headerRow, c: C });
        const cell = worksheet[cellRef];
        if (cell) {
          headers[C] = String(cell.v);
          console.log(`表头单元格 [${headerRow},${C}]: ${headers[C]}, 类型: ${cell.t}`);
        } else {
          headers[C] = '';
          console.log(`表头单元格 [${headerRow},${C}]: 空`);
        }
      }

      // 过滤掉空的表头
      const filteredHeaders = headers.filter(header => header.trim() !== '');
      console.log('过滤后的表头:', filteredHeaders);

      // 检查是否有足够的表头
      if (filteredHeaders.length < 3) { // 至少需要"模块"、"Key"和一种语言
        console.warn('表头数量不足，可能读取了错误的行。尝试读取下一行...');

        // 尝试读取下一行作为表头
        const nextHeaderRow = headerRow + 1;
        const nextHeaders: string[] = [];

        for (let C = 0; C <= range.e.c; C++) {
          const cellRef = XLSX.utils.encode_cell({ r: nextHeaderRow, c: C });
          const cell = worksheet[cellRef];
          nextHeaders[C] = cell ? String(cell.v) : '';
        }

        const filteredNextHeaders = nextHeaders.filter(header => header.trim() !== '');
        console.log('下一行表头:', filteredNextHeaders);

        // 如果下一行有更多的表头，使用它
        if (filteredNextHeaders.length > filteredHeaders.length) {
          console.log('使用下一行作为表头');
          originalInfo.headerRow = nextHeaderRow;
          headers.splice(0, headers.length, ...nextHeaders);
        }
      }

      console.log('最终使用的表头:', headers);

      // 解析表头中的语言配置
      const newLanguageConfig = parseLanguageConfigFromHeaders(headers);

      // 检查是否成功解析了所有语言列
      const expectedLanguageCount = headers.length - 2; // 减去"模块"和"Key"两列
      const actualLanguageCount = Object.keys(newLanguageConfig).length;

      if (actualLanguageCount < expectedLanguageCount) {
        console.warn(`可能有语言列未被正确解析。预期: ${expectedLanguageCount}, 实际: ${actualLanguageCount}`);

        // 找出未被解析的表头
        const parsedHeaders = new Set();
        Object.values(newLanguageConfig).forEach(config => {
          parsedHeaders.add(config.key);
        });

        const unparsedHeaders = headers.slice(2).filter(header => !parsedHeaders.has(header));
        console.warn('未解析的表头:', unparsedHeaders);

        // 尝试手动添加未解析的表头
        unparsedHeaders.forEach(header => {
          const parts = header.split('_');
          if (parts.length >= 2) {
            const label = parts[0];
            const langKeyParts = parts.slice(1);
            let langKey = '';

            if (langKeyParts.some(part => part.includes('values'))) {
              langKey = langKeyParts.join('_');
            } else if (langKeyParts.length > 0) {
              langKey = `values-${langKeyParts[0]}`;
            }

            if (langKey && !newLanguageConfig[langKey]) {
              console.log(`手动添加未解析的语言: ${label}, 键: ${langKey}, 原始表头: ${header}`);
              newLanguageConfig[langKey] = {
                key: header,
                label: label
              };
            }
          }
        });

        console.log('添加未解析表头后的语言配置:', newLanguageConfig);
      }

      console.log('解析出的语言配置:', newLanguageConfig);

      // 更新语言配置
      setLanguageConfig(newLanguageConfig);

      // 更新翻译服务中的语言提示
      updateLanguagePrompts(newLanguageConfig);

      // 格式化数据
      const formattedData = jsonData.map((row, index) => {
        const values: Record<string, string> = {};

        // 调试输出原始行数据
        if (index === 0) {
          console.log('原始行数据:', row);
        }

        // 为每种语言提取值
        Object.entries(newLanguageConfig).forEach(([langKey, config]) => {
          // 使用表头中定义的键名来获取值
          const headerKey = config.key;

          // 尝试多种方式获取值
          let value = '';

          // 1. 直接使用表头键
          if (row[headerKey] !== undefined) {
            value = row[headerKey];
          }
          // 2. 尝试使用表头键的第一部分（如"中文"而不是"中文_values-zh-rCN"）
          else if (headerKey.includes('_') && row[headerKey.split('_')[0]] !== undefined) {
            value = row[headerKey.split('_')[0]];
          }
          // 3. 尝试使用语言标签作为键
          else if (row[config.label] !== undefined) {
            value = row[config.label];
          }
          // 4. 尝试查找包含语言标签的键
          else {
            for (const colName of Object.keys(row)) {
              if (colName.includes(config.label)) {
                value = row[colName];
                break;
              }
            }
          }

          values[langKey] = value || '';

          // 调试输出
          if (index === 0) {
            console.log(`语言: ${config.label}, 键: ${langKey}, 表头键: ${headerKey}, 值: ${values[langKey]}`);
          }
        });

        // 尝试多种方式获取模块和Key
        let module = '';
        let key = '';

        // 查找模块列
        for (const colName of Object.keys(row)) {
          if (colName === '模块' || colName === 'module' || colName.toLowerCase().includes('module')) {
            module = row[colName] || '';
            break;
          }
        }

        // 查找Key列
        for (const colName of Object.keys(row)) {
          if (colName === 'Key' || colName === 'key' || colName.toLowerCase() === 'key') {
            key = row[colName] || '';
            break;
          }
        }

        // 如果没有找到Key，使用索引作为Key
        if (!key) {
          key = `row_${index}`;
        }

        if (index === 0) {
          console.log('提取的模块和Key:', { module, key });
          console.log('可用的列名:', Object.keys(row));
        }

        return {
          module,
          key,
          values
        };
      });

      // 检查是否存在重复的 key
      const keyCount = new Map<string, number>();

      // 统计每个 key 出现的次数
      formattedData.forEach(row => {
        const key = row.key;
        keyCount.set(key, (keyCount.get(key) || 0) + 1);
      });

      // 筛选出现次数大于1的 key（即重复的 key）
      const duplicateKeys = Array.from(keyCount.entries())
        .filter(([_, count]) => count > 1)
        .map(([key]) => key);

      // 如果存在重复的 key，显示警告但不阻止导入
      if (duplicateKeys.length > 0) {
        // 限制显示的重复key数量，避免提示框过长
        const displayKeys = duplicateKeys.length > 10
          ? duplicateKeys.slice(0, 10).join(', ') + `...等${duplicateKeys.length}个`
          : duplicateKeys.join(', ');
        message.warning(`Excel中存在相同的key，请注意检查和修正，否则将无法进行翻译操作。重复的key: ${displayKeys}`);
        console.warn('导入的数据中发现重复的 key:', duplicateKeys);
      }

      console.log('数据导入:', {
        原始数据量: jsonData.length,
        格式化后数据量: formattedData.length,
        语言配置: newLanguageConfig
      });

      // 更新数据
      setData(formattedData);
      dataRef.current = formattedData;

      setOriginalExcelInfo(originalInfo);
      message.success('导入成功');

      // 使用 requestAnimationFrame 确保在下一帧执行滚动
      requestAnimationFrame(() => {
        const tableBody = document.querySelector('.ant-table-body');
        if (tableBody) {
          const scrollHeight = tableBody.scrollHeight;
          const clientHeight = tableBody.clientHeight;
          const maxScrollTop = scrollHeight - clientHeight;

          // 先滚动到倒数第二屏
          tableBody.scrollTop = maxScrollTop - clientHeight;

          // 然后延迟滚动到最底部
          setTimeout(() => {
            tableBody.scrollTop = maxScrollTop;
          }, 50);
        }
      });
    } catch (error) {
      message.error('导入失败');
      console.error('Import error:', error);
    }
  };

  const handleTranslate = async () => {
    // 检查是否存在重复的 key
    const keyCount = new Map<string, number>();

    // 统计每个 key 出现的次数
    data.forEach(row => {
      const key = row.key;
      keyCount.set(key, (keyCount.get(key) || 0) + 1);
    });

    // 筛选出现次数大于1的 key（即重复的 key）
    const duplicateKeys = Array.from(keyCount.entries())
      .filter(([_, count]) => count > 1)
      .map(([key]) => key);

    // 如果存在重复的 key，显示警告并终止翻译
    if (duplicateKeys.length > 0) {
      // 限制显示的重复key数量，避免提示框过长
      const displayKeys = duplicateKeys.length > 10
        ? duplicateKeys.slice(0, 10).join(', ') + `...等${duplicateKeys.length}个`
        : duplicateKeys.join(', ');
      message.error(`存在相同的key，请检查。重复的key: ${displayKeys}`);
      console.error('发现重复的 key:', duplicateKeys);
      return;
    }

    // 查找中文语言键
    const chineseKey = Object.keys(languageConfig).find(key =>
      languageConfig[key].label.includes('中文') || key === 'values-zh-rCN'
    ) || 'values-zh-rCN';

    // 计算总共需要翻译的条目数
    const translationTasks: Array<{
      row: LanguageData;
      langKey: string;
      langConfig: LanguageConfig;
    }> = [];

    data.forEach(row => {
      const chineseValue = row.values[chineseKey];
      if (chineseValue) {
        Object.entries(languageConfig).forEach(([key, config]) => {
          if (key !== chineseKey && !row.values[key]) {
            translationTasks.push({ row, langKey: key, langConfig: config });
          }
        });
      }
    });

    if (translationTasks.length === 0) {
      message.info('没有需要翻译的内容');
      return;
    }

    // 设置进度条初始状态
    setTranslationProgress({
      visible: true,
      current: 0,
      total: translationTasks.length,
      currentLanguage: '',
      currentText: '',
      queueStatus: {
        activeRequests: 0,
        queueLength: 0
      }
    });

    const newData = [...data];

    try {
      for (let i = 0; i < translationTasks.length; i++) {
        const { row, langKey, langConfig } = translationTasks[i];
        const chineseValue = row.values[chineseKey];
        const rowIndex = newData.findIndex(item => item.key === row.key);

        // 更新进度和当前翻译信息
        setTranslationProgress(prev => ({
          ...prev,
          current: i,
          currentLanguage: `${langConfig.label}`,
          currentText: chineseValue
        }));

        try {
          newData[rowIndex].values[langKey] = await translateText(chineseValue, langKey);
        } catch (error) {
          console.error(`翻译失败 (${row.key} - ${langConfig.label}):`, error);
        }
      }

      // 设置最终进度
      setTranslationProgress(prev => ({
        ...prev,
        current: translationTasks.length
      }));

      setData(newData);
      message.success('所有翻译完成');
    } catch (error) {
      message.error('翻译过程中出现错误');
    } finally {
      // 延迟关闭进度条
      setTimeout(() => {
        setTranslationProgress(prev => ({ ...prev, visible: false }));
      }, 500);
    }
  };

  const handleExport = () => {
    // 实现导出逻辑
  }

  // 翻译单行数据
  const handleTranslateRow = async (record: LanguageData) => {
    try {
      if (data.length === 0) {
        message.error('数据状态异常，请重新导入数据');
        return;
      }

      // 检查是否存在重复的 key
      const keyCount = new Map<string, number>();

      // 统计每个 key 出现的次数
      data.forEach(row => {
        const key = row.key;
        keyCount.set(key, (keyCount.get(key) || 0) + 1);
      });

      // 筛选出现次数大于1的 key（即重复的 key）
      const duplicateKeys = Array.from(keyCount.entries())
        .filter(([_, count]) => count > 1)
        .map(([key]) => key);

      // 如果存在重复的 key，显示警告并终止翻译
      if (duplicateKeys.length > 0) {
        // 限制显示的重复key数量，避免提示框过长
        const displayKeys = duplicateKeys.length > 10
          ? duplicateKeys.slice(0, 10).join(', ') + `...等${duplicateKeys.length}个`
          : duplicateKeys.join(', ');
        message.error(`存在相同的key，请检查。重复的key: ${displayKeys}`);
        console.error('发现重复的 key:', duplicateKeys);
        return;
      }

      // 查找中文语言键
      const chineseKey = Object.keys(languageConfig).find(key =>
        languageConfig[key].label.includes('中文') || key === 'values-zh-rCN'
      ) || 'values-zh-rCN';

      // 创建数据的深拷贝
      const newData = JSON.parse(JSON.stringify(data));
      const rowIndex = newData.findIndex((item: LanguageData) => item.key === record.key);

      if (rowIndex === -1) {
        message.error('找不到要翻译的行');
        return;
      }

      // 创建翻译任务
      const translationTasks: Array<{
        langKey: string;
        langConfig: LanguageConfig;
      }> = [];

      Object.entries(languageConfig).forEach(([key, config]) => {
        if (key !== chineseKey && !record.values[key]) {
          translationTasks.push({ langKey: key, langConfig: config });
        }
      });

      if (translationTasks.length === 0) {
        message.info('该行所有语言已翻译完成');
        return;
      }

      setTranslationProgress({
        visible: true,
        current: 0,
        total: translationTasks.length,
        currentLanguage: '',
        currentText: record.values[chineseKey],
        queueStatus: {
          activeRequests: 0,
          queueLength: 0
        }
      });

      for (let i = 0; i < translationTasks.length; i++) {
        const { langKey, langConfig } = translationTasks[i];

        setTranslationProgress(prev => ({
          ...prev,
          current: i,
          currentLanguage: langConfig.label,
          currentText: record.values[chineseKey]
        }));

        try {
          const translatedText = await translateText(record.values[chineseKey], langKey);
          newData[rowIndex].values[langKey] = translatedText;
          setData([...newData]);
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '未知错误';
          message.error(`翻译 ${langConfig.label} 失败: ${errorMessage}`);
        }
      }

      message.success('翻译完成');
    } catch (error) {
      console.error('翻译过程中出现错误:', error);
      message.error('翻译过程中出现错误');
    } finally {
      setTranslationProgress(prev => ({ ...prev, visible: false }));
    }
  };

  // 添加新行
  const handleAddRow = async ({
    key,
    chinese,
    module
  }: {
    key: string;
    chinese: string;
    module: string;
  }) => {
    try {
      // 检查 key 是否重复
      if (data.some(item => item.key === key)) {
        message.error('Key 已存在');
        return;
      }

      // 查找中文语言键
      const chineseKey = Object.keys(languageConfig).find(key =>
        languageConfig[key].label.includes('中文') || key === 'values-zh-rCN'
      ) || 'values-zh-rCN';

      // 创建新行数据
      const newRow: LanguageData = {
        module,
        key,
        values: Object.keys(languageConfig).reduce((acc, langKey) => {
          acc[langKey] = langKey === chineseKey ? chinese : '';
          return acc;
        }, {} as Record<string, string>)
      };

      // 使用 Promise 来确保数据更新完成
      await new Promise<void>(resolve => {
        setData(prevData => {
          const newData = [...prevData, newRow];
          resolve();
          return newData;
        });
      });

      setAddRowModalVisible(false);
      message.success('添加成功');

      // 使用 requestAnimationFrame 确保在下一帧执行滚动
      requestAnimationFrame(() => {
        const tableBody = document.querySelector('.ant-table-body');
        if (tableBody) {
          const scrollHeight = tableBody.scrollHeight;
          const clientHeight = tableBody.clientHeight;
          const maxScrollTop = scrollHeight - clientHeight;

          // 先滚动到倒数第二屏
          tableBody.scrollTop = maxScrollTop - clientHeight;

          // 然后延迟滚动到最底部
          setTimeout(() => {
            tableBody.scrollTop = maxScrollTop;
          }, 50);
        }
      });

      // 现在可以安全地调用翻译函数
      // await handleTranslateRow(newRow);

    } catch (error) {
      console.error('添加新行失败:', error);
      message.error('添加失败');
    }
  };

  // 处理一键翻译按钮点击
  const handleTranslateClick = async () => {
    setSelectorVisible(true);
    // 返回一个永远解析的 Promise
    return Promise.resolve();
  };

  // 处理选择的行进行翻译
  const handleSelectedRowsTranslate = async (selectedRows: LanguageData[]) => {
    setSelectorVisible(false);

    // 声明定时器变量
    let queueStatusTimer: NodeJS.Timeout | null = null;

    try {
      // 检查整个数据集中是否存在重复的 key
      const keyCount = new Map<string, number>();

      // 统计每个 key 出现的次数
      data.forEach(row => {
        const key = row.key;
        keyCount.set(key, (keyCount.get(key) || 0) + 1);
      });

      // 筛选出现次数大于1的 key（即重复的 key）
      const duplicateKeys = Array.from(keyCount.entries())
        .filter(([_, count]) => count > 1)
        .map(([key]) => key);

      // 如果存在重复的 key，显示警告并终止翻译
      if (duplicateKeys.length > 0) {
        // 限制显示的重复key数量，避免提示框过长
        const displayKeys = duplicateKeys.length > 10
          ? duplicateKeys.slice(0, 10).join(', ') + `...等${duplicateKeys.length}个`
          : duplicateKeys.join(', ');
        message.error(`存在相同的key，请检查。重复的key: ${displayKeys}`);
        console.error('发现重复的 key:', duplicateKeys);
        return;
      }

      // 查找中文语言键
      const chineseKey = Object.keys(languageConfig).find(key =>
        languageConfig[key].label.includes('中文') || key === 'values-zh-rCN'
      ) || 'values-zh-rCN';

      // 创建翻译任务列表
      const translationTasks: Array<{
        rowIndex: number;
        row: LanguageData;
        langKey: string;
        langConfig: LanguageConfig;
      }> = [];

      // 创建数据的深拷贝
      const newData = JSON.parse(JSON.stringify(data));

      // 准备翻译任务
      selectedRows.forEach(selectedRow => {
        const rowIndex = newData.findIndex((item: LanguageData) => item.key === selectedRow.key);
        if (rowIndex === -1) return;

        Object.entries(languageConfig).forEach(([key, config]) => {
          if (key !== chineseKey && !selectedRow.values[key]) {
            translationTasks.push({
              rowIndex,
              row: selectedRow,
              langKey: key,
              langConfig: config
            });
          }
        });
      });

      if (translationTasks.length === 0) {
        message.info('没有需要翻译的内容');
        return;
      }

      // 设置进度条初始状态
      setTranslationProgress({
        visible: true,
        current: 0,
        total: translationTasks.length,
        currentLanguage: '',
        currentText: '',
        queueStatus: {
          activeRequests: 0,
          queueLength: 0
        }
      });

      // 用于追踪已完成的翻译数量
      let completedCount = 0;

      // 创建定时器，定期更新队列状态
      queueStatusTimer = setInterval(() => {
        const status = getQueueStatus();
        setTranslationProgress(prev => ({
          ...prev,
          queueStatus: status
        }));
      }, 500);

      // 创建并发翻译请求
      const translationPromises = translationTasks.map(async (task) => {
        const { rowIndex, row, langKey, langConfig } = task;
        const chineseValue = row.values[chineseKey];

        try {
          // 更新当前正在翻译的信息
          setTranslationProgress(prev => ({
            ...prev,
            currentLanguage: langConfig.label,
            currentText: chineseValue
          }));

          // 获取翻译结果
          const translatedText = await translateText(chineseValue, langKey);

          // 更新数据
          newData[rowIndex].values[langKey] = translatedText;

          // 更新已完成数量和进度条
          completedCount++;
          setTranslationProgress(prev => ({
            ...prev,
            current: completedCount,
            currentLanguage: langConfig.label,
            currentText: chineseValue,
            queueStatus: getQueueStatus() // 更新最新的队列状态
          }));

          return {
            success: true,
            rowIndex,
            langKey,
            translatedText
          };
        } catch (error) {
          console.error(`翻译失败 (${row.key} - ${langConfig.label}):`, error);

          // 即使失败也要更新进度
          completedCount++;
          setTranslationProgress(prev => ({
            ...prev,
            current: completedCount,
            queueStatus: getQueueStatus() // 更新最新的队列状态
          }));

          return {
            success: false,
            error: error,
            rowIndex,
            langKey,
            langLabel: langConfig.label
          };
        }
      });

      // 等待所有翻译完成
      const results = await Promise.allSettled(translationPromises);

      // 清除队列状态更新定时器
      clearInterval(queueStatusTimer);

      // 更新数据
      setData(newData);

      // 统计翻译结果
      const failedTranslations = results.filter(
        (result): result is PromiseFulfilledResult<{
          success: false;
          error: unknown;
          rowIndex: number;
          langKey: string;
          langLabel: string;
        }> =>
          result.status === 'fulfilled' && !result.value.success
      );

      if (failedTranslations.length > 0) {
        // 显示失败的翻译
        const failedLanguages = failedTranslations.map(result => result.value.langLabel).join(', ');
        message.warning(`部分翻译失败 (${failedLanguages})`);
      } else {
        message.success('所有翻译完成');
      }

    } catch (error) {
      console.error('翻译过程中出现错误:', error);
      message.error('翻译过程中出现错误');

      // 确保在错误情况下也清除定时器
      if (queueStatusTimer) {
        clearInterval(queueStatusTimer);
      }
    } finally {
      // 延迟关闭进度条
      setTimeout(() => {
        setTranslationProgress(prev => ({ ...prev, visible: false }));
      }, 500);
    }
  };

  // 添加数据状态检查
  useEffect(() => {
    console.log('数据状态检查:', {
      时间: new Date().toISOString(),
      数据量: data.length,
      数据有效: Array.isArray(data),
      示例数据: data[0]
    });
  }, [data]);

  return (
    <div className="app">
      <TitleBar />
      <div className="main-content">
        <ImportExport
          onDataImported={handleDataImported}
          onTranslate={handleTranslateClick}
          data={data}
          originalExcelInfo={originalExcelInfo}
          languageConfig={languageConfig}
        />
        <LanguageTable
          data={data}
          languageConfig={languageConfig}
          onAddNewRow={() => setAddRowModalVisible(true)}
        />
        <TranslationProgress {...translationProgress} />
        <TranslationSelector
          visible={selectorVisible}
          onCancel={() => setSelectorVisible(false)}
          onOk={handleSelectedRowsTranslate}
          data={data}
        />
        <AddRowModal
          visible={addRowModalVisible}
          onCancel={() => setAddRowModalVisible(false)}
          onOk={handleAddRow}
        />
      </div>
    </div>
  )
}

export default App
