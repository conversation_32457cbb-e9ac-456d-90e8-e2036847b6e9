{"name": "imcam-translator", "version": "1.0.7", "main": "dist-electron/main/index.js", "description": "Imcam app translator.", "author": "iYuuki", "private": true, "debug": {"env": {"VITE_DEV_SERVER_URL": "http://127.0.0.1:7777/"}}, "scripts": {"dev": "vite", "build": "rimraf dist dist-electron && vite build && electron-builder", "build:dir": "rimraf dist dist-electron && vite build && electron-builder --dir", "preview": "vite preview", "pree2e": "vite build --mode=test", "e2e": "playwright test", "setup": "node scripts/setup-config.js", "postinstall": "node scripts/setup-config.js"}, "dependencies": {"antd": "^5.24.2", "axios": "^1.8.1", "electron-updater": "^6.3.9", "xlsx": "^0.18.5"}, "devDependencies": {"@playwright/test": "^1.48.2", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/xlsx": "^0.0.35", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "electron": "^33.2.0", "electron-builder": "^24.13.3", "postcss": "^8.4.49", "postcss-import": "^16.1.0", "react": "^18.3.1", "react-dom": "^18.3.1", "rimraf": "^6.0.1", "sass-embedded": "^1.85.1", "tailwindcss": "^3.4.15", "typescript": "^5.4.2", "vite": "^5.4.11", "vite-plugin-electron": "^0.29.0", "vite-plugin-electron-renderer": "^0.14.6", "vitest": "^2.1.5"}}