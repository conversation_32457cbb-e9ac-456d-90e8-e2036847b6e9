import React, { useState } from 'react';
import * as XLSX from 'xlsx';
import { Button, message, Upload, Spin } from 'antd';
import { UploadOutlined, TranslationOutlined, ExportOutlined } from '@ant-design/icons';
import type { UploadFile } from 'antd/es/upload/interface';
import './ImportExport.scss';
import { LanguageData, LanguageConfig } from '../types';

// 处理文本值，确保空格不会影响导出格式
function formatCellValue(value: string): string {
  // 如果值为空或不是字符串，直接返回
  if (!value || typeof value !== 'string') {
    return value;
  }

  // 处理包含空格的文本，特别是中文
  // 这里我们使用非断行空格替换普通空格，防止Excel处理问题
  if (value.includes(' ')) {
    // 将普通空格替换为非断行空格（Unicode: \u00A0）
    return value.replace(/ /g, '\u00A0');
  }

  return value;
}

interface ImportExportProps {
  onDataImported: (data: any[], originalInfo: any) => void;
  onTranslate: () => Promise<void>;
  data: LanguageData[];
  originalExcelInfo: {
    workbook: any;
    sheetName: string;
    headerRow: number;
    fileName: string;
  } | null;
  languageConfig: Record<string, LanguageConfig>;
}

export const ImportExport: React.FC<ImportExportProps> = ({
  onDataImported,
  onTranslate,
  data,
  originalExcelInfo,
  languageConfig
}) => {
  const [isTranslating, setIsTranslating] = useState(false);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [importing, setImporting] = useState(false);

  // 文件大小限制 (20MB) - 降低限制以避免内存问题
  const MAX_FILE_SIZE = 20 * 1024 * 1024;
  // 单元格数量限制
  const MAX_CELL_COUNT = 500000; // 50万个单元格

  // 内存使用监控函数
  const checkMemoryUsage = () => {
    if (window.performance && (window.performance as any).memory) {
      const memoryInfo = (window.performance as any).memory;
      console.log('内存使用情况:', {
        总堆大小: `${(memoryInfo.totalJSHeapSize / (1024 * 1024)).toFixed(2)}MB`,
        已用堆大小: `${(memoryInfo.usedJSHeapSize / (1024 * 1024)).toFixed(2)}MB`,
        堆大小限制: `${(memoryInfo.jsHeapSizeLimit / (1024 * 1024)).toFixed(2)}MB`,
        使用率: `${((memoryInfo.usedJSHeapSize / memoryInfo.jsHeapSizeLimit) * 100).toFixed(2)}%`
      });

      // 如果内存使用超过70%，发出警告
      if (memoryInfo.usedJSHeapSize / memoryInfo.jsHeapSizeLimit > 0.7) {
        console.warn('内存使用率过高，可能导致应用崩溃');
        return true;
      }
    }
    return false;
  };

  const handleImport = async (file: File) => {
    try {
      // 检查文件大小
      if (file.size > MAX_FILE_SIZE) {
        message.error(`文件过大，请将文件拆分为多个较小的文件。最大支持 ${MAX_FILE_SIZE / 1024 / 1024}MB`);
        return false;
      }

      setImporting(true);

      // 检查初始内存使用情况
      checkMemoryUsage();

      console.log(`开始处理文件: ${file.name}, 大小: ${(file.size / 1024 / 1024).toFixed(2)}MB`);

      // 读取Excel文件
      let workbook: any, sheetName: string, worksheet: any, range: any, headers: string[] = [];

      try {
        // 使用FileReader读取文件
        const fileData = await new Promise<ArrayBuffer>((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = (e: ProgressEvent<FileReader>) => resolve(e.target?.result as ArrayBuffer);
          reader.onerror = () => reject(new Error('文件读取失败'));
          reader.readAsArrayBuffer(file);
        });

        // 使用更严格的选项读取Excel
        const data = new Uint8Array(fileData);
        workbook = XLSX.read(data, {
          type: 'array',
          cellStyles: true,  // 禁用样式以减少内存使用
          cellFormula: false, // 禁用公式解析以减少内存使用
          cellHTML: false,    // 禁用HTML解析以减少内存使用
          cellText: true,     // 启用文本解析
          cellDates: false,   // 禁用日期对象以减少内存使用
          sheetStubs: false,  // 忽略空单元格以减少内存使用
          bookVBA: false,     // 忽略VBA以减少内存使用
          bookDeps: false,    // 忽略依赖以减少内存使用
          bookFiles: false,   // 忽略文件以减少内存使用
          bookProps: false,   // 忽略属性以减少内存使用
          bookSheets: false,  // 忽略工作表列表以减少内存使用
          WTF: false          // 关闭调试模式
        });

        if (!workbook || !workbook.SheetNames || workbook.SheetNames.length === 0) {
          throw new Error('Excel文件格式无效或不包含任何工作表');
        }

        sheetName = workbook.SheetNames[0];
        worksheet = workbook.Sheets[sheetName];

        if (!worksheet) {
          throw new Error('无法读取Excel工作表');
        }

        // 获取工作表范围
        range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');
        console.log('Excel工作表范围:', range);

        // 检查数据量是否过大
        const totalCells = (range.e.r + 1) * (range.e.c + 1);
        console.log(`Excel文件总单元格数: ${totalCells.toLocaleString()}`);

        if (totalCells > MAX_CELL_COUNT) {
          throw new Error(`Excel文件过大 (${totalCells.toLocaleString()}个单元格)，请拆分为多个较小的文件。最大支持${MAX_CELL_COUNT.toLocaleString()}个单元格`);
        }

        // 检查内存使用情况
        if (checkMemoryUsage()) {
          throw new Error('内存使用率过高，无法继续处理。请尝试使用更小的文件。');
        }

        // 读取表头
        headers = [];
        const headerRow = 0; // 表头在第0行

        try {
          // 使用XLSX的sheet_to_json函数读取第一行作为表头
          const headerData = XLSX.utils.sheet_to_json(worksheet, {
            header: 1,
            range: { s: { r: 0, c: 0 }, e: { r: 0, c: range.e.c } }
          }) as any[];

          if (headerData && headerData.length > 0) {
            // 第一行数据作为表头
            const headerRowData = headerData[0];
            for (let C = 0; C < headerRowData.length; C++) {
              headers[C] = headerRowData[C] ? String(headerRowData[C]) : '';
            }

            // 填充缺失的列
            for (let C = headerRowData.length; C <= range.e.c; C++) {
              headers[C] = '';
            }
          }
        } catch (headerError) {
          console.error('读取表头错误:', headerError);

          // 回退到逐个单元格读取表头
          console.log('回退到逐个单元格读取表头');
          for (let C = 0; C <= range.e.c; C++) {
            const cellRef = XLSX.utils.encode_cell({ r: headerRow, c: C });
            const cell = worksheet[cellRef];
            if (cell) {
              headers[C] = String(cell.v);
            } else {
              headers[C] = '';
            }
          }
        }

        // 记录表头信息
        console.log('读取到的表头:', headers.filter((h: string) => h).join(', '));
      } catch (error) {
        console.error('Excel文件处理错误:', error);
        throw new Error(`Excel文件处理失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }

      // 创建一个轻量级的原始Excel信息对象
      const originalInfo = {
        workbook: {
          SheetNames: [sheetName],
          Sheets: {
            [sheetName]: worksheet
          }
        },
        sheetName,
        headerRow: 0,
        fileName: file.name
      };

      // 分批处理数据
      try {
        // 使用更小的批次大小
        const totalRows = range.e.r - 1; // 减去表头行
        const batchSize = 500; // 每批处理500行，减少内存压力
        const batches = Math.ceil(totalRows / batchSize);

        console.log(`开始分批处理数据: 总行数=${totalRows}, 批次大小=${batchSize}, 总批次=${batches}`);

        // 使用更高效的数据结构
        let allData: any[] = [];
        let needsMapping = false;

        // 处理第一批数据以确定是否需要映射
        if (totalRows > 0) {
          console.log(`处理第一批数据以确定格式...`);

          // 只读取第一批的第一行来确定格式
          const sampleData = XLSX.utils.sheet_to_json(worksheet, {
            raw: false,
            defval: '',
            header: headers,
            range: {
              s: { r: 1, c: 0 },
              e: { r: Math.min(10, range.e.r), c: range.e.c }
            }
          });

          if (sampleData.length > 0) {
            const firstRow = sampleData[0];
            needsMapping = Object.keys(firstRow).some(key => !isNaN(Number(key)));
            console.log(`数据格式检测: ${needsMapping ? '需要映射索引到表头' : '已使用表头作为键，无需映射'}`);
          }
        }

        // 分批处理数据
        for (let batch = 0; batch < batches; batch++) {
          const startRow = 1 + (batch * batchSize); // 从第1行开始（跳过表头）
          const endRow = Math.min(startRow + batchSize - 1, range.e.r);

          console.log(`处理批次 ${batch + 1}/${batches}: 行 ${startRow} 到 ${endRow}`);

          // 检查内存使用情况
          if (checkMemoryUsage()) {
            console.warn(`批次 ${batch + 1} 处理前内存使用率过高，尝试释放内存...`);

            // 尝试释放内存
            try {
              console.log('尝试释放内存...');

              // 创建一个大对象然后释放它，可能有助于触发垃圾回收
              const largeArray = new Array(10000).fill(0);
              largeArray.length = 0;

              // 等待一小段时间，给垃圾回收器一个机会
              await new Promise(resolve => setTimeout(resolve, 100));
            } catch (e) {
              console.error('内存释放失败:', e);
            }

            // 如果内存仍然过高，可能需要中断处理
            if (checkMemoryUsage()) {
              console.error('内存使用率仍然过高，无法继续处理');
              if (batch > 0) {
                // 如果已经处理了一些数据，可以选择继续使用已处理的数据
                console.warn(`将只使用已处理的 ${allData.length} 行数据`);
                break;
              } else {
                throw new Error('内存不足，无法处理此文件。请尝试使用更小的文件。');
              }
            }
          }

          // 只读取当前批次的数据
          let batchData = XLSX.utils.sheet_to_json(worksheet, {
            raw: false,
            defval: '',  // 空单元格的默认值
            header: headers, // 使用读取到的表头作为列名
            range: {     // 指定当前批次的范围
              s: { r: startRow, c: 0 },
              e: { r: endRow, c: range.e.c }
            }
          });

          // 如果需要映射，处理当前批次
          if (needsMapping) {
            // 使用更高效的映射方法
            const validHeaders = headers.filter((h: string) => h);
            batchData = batchData.map((row: any) => {
              const newRow: any = {};
              // 只处理有效的表头
              validHeaders.forEach((header: string, index: number) => {
                newRow[header] = row[index] || '';
              });
              return newRow;
            });
          }

          // 添加到总数据中
          allData = allData.concat(batchData);

          // 释放批次数据内存
          batchData = null as any;

          // 检查处理后的内存使用情况
          console.log(`批次 ${batch + 1} 处理完成，当前数据量: ${allData.length} 行`);
          checkMemoryUsage();

          // 给UI线程一些时间来响应
          if (batch < batches - 1) {
            await new Promise(resolve => setTimeout(resolve, 10)); // 增加暂停时间
          }
        }

        console.log(`所有批次处理完成，总数据量: ${allData.length} 行`);

        // 检查处理后的数据
        if (allData.length > 0) {
          console.log('处理后的数据示例:', JSON.stringify(allData[0]).substring(0, 200) + '...');
        } else {
          console.log('处理后无数据');
        }

        // 最终内存检查
        checkMemoryUsage();

        // 尝试释放不再需要的内存
        worksheet = null as any;

        // 导入数据
        onDataImported(allData, originalInfo);
        message.success('导入成功');
      } catch (processingError) {
        console.error('数据处理错误:', processingError);
        throw new Error(`数据处理失败: ${processingError instanceof Error ? processingError.message : '未知错误'}`);
      }
    } catch (error) {
      console.error('导入失败:', error);
      message.error(`导入失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setImporting(false);
    }

    return false; // 阻止默认上传行为
  };

  const handleExport = async () => {
    try {
      if (!data || data.length === 0) {
        message.warning('没有可导出的数据');
        return;
      }

      if (!originalExcelInfo) {
        message.warning('没有原始文件信息，无法进行导出');
        return;
      }

      // 检查数据量是否过大
      if (data.length > 50000) {
        message.warning(`数据量较大 (${data.length.toLocaleString()} 行)，导出可能需要较长时间，请耐心等待`);
      }

      console.log('渲染进程: 开始导出数据:', {
        数据量: data.length,
        原始文件名: originalExcelInfo.fileName
      });

      // 使用原始工作簿
      const workbook = originalExcelInfo.workbook;
      const sheet = workbook.Sheets[originalExcelInfo.sheetName];

      // 创建数据映射
      const dataMap = new Map(data.map(row => [row.key, row]));

      // 获取原始数据的范围
      const range = XLSX.utils.decode_range(sheet['!ref'] || 'A1');

      // 保存所有特殊属性
      const sheetProps = {
        '!merges': sheet['!merges'],
        '!rows': sheet['!rows'],
        '!cols': sheet['!cols'],
        '!validations': sheet['!validations'],
        '!autofilter': sheet['!autofilter']
      };

      // 记录已更新的键
      const updatedKeys = new Set();

      // 使用分批处理更新现有行的数据
      const batchSize = 1000; // 每批处理1000行
      const totalRows = range.e.r - originalExcelInfo.headerRow + 1;
      const batches = Math.ceil(totalRows / batchSize);

      console.log(`开始分批更新数据: 总行数=${totalRows}, 批次大小=${batchSize}, 总批次=${batches}`);

      for (let batch = 0; batch < batches; batch++) {
        const startRow = originalExcelInfo.headerRow + (batch * batchSize);
        const endRow = Math.min(startRow + batchSize - 1, range.e.r);

        console.log(`处理批次 ${batch + 1}/${batches}: 行 ${startRow} 到 ${endRow}`);

        // 更新当前批次的行
        for (let R = startRow; R <= endRow; R++) {
          const keyCell = sheet[XLSX.utils.encode_cell({ r: R, c: 1 })];
          if (!keyCell) continue;

          const key = keyCell.v;
          const newData = dataMap.get(key);
          if (!newData) continue;

          // 更新语言值
          Object.entries(languageConfig).forEach(([langKey, _config], index) => {
            const col = index + 2;
            const cellRef = XLSX.utils.encode_cell({ r: R, c: col });
            const newValue = newData.values[langKey];
            const oldCell = sheet[cellRef];

            if (newValue !== undefined) {
              sheet[cellRef] = {
                ...oldCell,  // 保留原有属性
                v: newValue,
                w: newValue,
              };
            }
          });

          updatedKeys.add(key);
        }

        // 给UI线程一些时间来响应
        if (batch < batches - 1) {
          await new Promise(resolve => setTimeout(resolve, 0));
        }
      }

      console.log(`所有现有行更新完成，已更新 ${updatedKeys.size} 行`);

      // 处理新增的数据
      const newData = Array.from(dataMap.entries())
        .filter(([key]) => !updatedKeys.has(key));

      if (newData.length > 0) {
        console.log('渲染进程: 添加新数据:', newData.length, '行');

        // 获取最后一行的样式作为模板
        const templateRow = range.e.r;
        const templateCells: { [key: number]: any } = {};

        // 复制最后一行的单元格格式
        for (let c = 0; c <= range.e.c; c++) {
          const cellRef = XLSX.utils.encode_cell({ r: templateRow, c });
          const cell = sheet[cellRef];
          if (cell) {
            templateCells[c] = {
              s: cell.s || {},  // 样式
              z: cell.z,        // 数字格式
              f: cell.f,        // 公式
              t: cell.t,        // 类型
            };
          }
        }

        // 分批添加新行
        let newRowIndex = range.e.r + 1;
        const newRowBatchSize = 500; // 每批添加500行
        const newRowBatches = Math.ceil(newData.length / newRowBatchSize);

        console.log(`开始分批添加新行: 总行数=${newData.length}, 批次大小=${newRowBatchSize}, 总批次=${newRowBatches}`);

        for (let batch = 0; batch < newRowBatches; batch++) {
          const startIdx = batch * newRowBatchSize;
          const endIdx = Math.min(startIdx + newRowBatchSize, newData.length);

          console.log(`处理新行批次 ${batch + 1}/${newRowBatches}: 索引 ${startIdx} 到 ${endIdx - 1}`);

          // 处理当前批次的新行
          for (let i = startIdx; i < endIdx; i++) {
            const [key, rowData] = newData[i];

            // 添加模块列
            sheet[XLSX.utils.encode_cell({ r: newRowIndex, c: 0 })] = {
              ...templateCells[0],
              v: rowData.module,
              w: rowData.module,
              t: 's'
            };

            // 添加Key列
            sheet[XLSX.utils.encode_cell({ r: newRowIndex, c: 1 })] = {
              ...templateCells[1],
              v: key,
              w: key,
              t: 's'
            };

            // 添加语言列
            Object.entries(languageConfig).forEach(([langKey, _config], index) => {
              const col = index + 2;
              const value = rowData.values[langKey] || '';
              sheet[XLSX.utils.encode_cell({ r: newRowIndex, c: col })] = {
                ...templateCells[col],
                v: value,
                w: value,
                t: 's'
              };
            });

            newRowIndex++;
          }

          // 给UI线程一些时间来响应
          if (batch < newRowBatches - 1) {
            await new Promise(resolve => setTimeout(resolve, 0));
          }
        }

        console.log(`所有新行添加完成，共添加 ${newData.length} 行`);


        // 更新表格范围
        sheet['!ref'] = XLSX.utils.encode_range({
          s: { r: 0, c: 0 },
          e: { r: newRowIndex - 1, c: range.e.c }
        });
      }

      // 恢复特殊属性
      Object.entries(sheetProps).forEach(([key, value]) => {
        if (value) {
          sheet[key] = value;
        }
      });

      console.log('渲染进程: 开始生成Excel数据');

      try {
        // 显示加载提示
        message.loading('正在生成Excel文件，请稍候...', 0);

        // 给UI线程一些时间来显示加载提示
        await new Promise(resolve => setTimeout(resolve, 100));

        // 转换为二进制数据，使用优化选项
        const wbout = XLSX.write(workbook, {
          bookType: 'xlsx',
          type: 'buffer',
          cellStyles: true,
          compression: true,
          bookSST: true,
          WTF: false,       // 关闭调试模式
          cellDates: false  // 不使用日期对象，减少内存使用
        });

        console.log('渲染进程: Excel数据生成完成，准备保存');

        // 生成文件名
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
        const fileNameWithoutExt = originalExcelInfo.fileName.replace(/\.xlsx$/, '');
        const defaultFileName = `${fileNameWithoutExt}_${timestamp}.xlsx`;

        // 关闭加载提示
        message.destroy();

        // 保存对话框
        const dialogResult = await window.electron.showSaveDialog({
          defaultPath: defaultFileName,
          filters: [{ name: 'Excel Files', extensions: ['xlsx'] }]
        });

        if (!dialogResult || dialogResult.canceled) {
          console.log('渲染进程: 用户取消了保存操作');
          return;
        }

        // 显示保存中提示
        message.loading('正在保存文件，请稍候...', 0);

        // 保存文件
        const saveResult = await window.electron.saveExcelFile({
          buffer: Array.from(new Uint8Array(wbout)),
          filePath: dialogResult.filePath
        });

        // 关闭加载提示
        message.destroy();

        if (saveResult.success) {
          message.success('导出成功');
        } else {
          throw new Error(saveResult.error || '保存文件失败');
        }

        // 释放内存
        // 这里我们不能直接设置 workbook = null，因为它是从 originalExcelInfo 中引用的
        // 但我们可以建议垃圾回收
        console.log('渲染进程: 导出完成，释放内存');

      } catch (exportError) {
        // 关闭加载提示
        message.destroy();
        throw exportError;
      }
    } catch (error) {
      console.error('渲染进程: 导出失败:', error);
      message.error(`导出失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  const handleTranslate = async () => {
    setIsTranslating(true);
    try {
      await onTranslate();
      message.success('翻译完成');
    } catch (error) {
      message.error('翻译失败');
    } finally {
      setIsTranslating(false);
    }
  };

  return (
    <div className="import-export">
      <Spin spinning={importing}>
        <Upload
          accept=".xlsx"
          fileList={fileList}
          beforeUpload={handleImport}
          onChange={({ fileList }) => setFileList(fileList)}
          maxCount={1}
        >
          <Button icon={<UploadOutlined />}>导入多语言表格</Button>
        </Upload>
      </Spin>

      <Button
        icon={<TranslationOutlined />}
        onClick={handleTranslate}
        loading={isTranslating}
        type="primary"
      >
        一键翻译
      </Button>

      <Button
        icon={<ExportOutlined />}
        onClick={handleExport}
        disabled={!originalExcelInfo}
      >
        导出
      </Button>
    </div>
  );
};